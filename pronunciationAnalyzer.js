/**
 * pronunciationAnalyzer.js - Advanced pronunciation and pacing analysis
 * Analyzes Whisper transcription results to detect mispronunciations and pacing issues
 */

/**
 * Analyze transcription for pronunciation and pacing issues
 * @param {string} originalText - The expected text
 * @param {Object} whisperResult - Whisper transcription result with segments and word timing
 * @returns {Object} Analysis results with detected issues and recommendations
 */
export function analyzeTranscription(originalText, whisperResult) {
    const analysis = {
        pronunciationIssues: [],
        pacingIssues: [],
        overallScore: 0,
        recommendations: [],
        detailedComparison: null
    };

    if (!whisperResult || !whisperResult.transcript) {
        analysis.recommendations.push("No transcription data available for analysis.");
        return analysis;
    }

    // Perform word-level comparison
    analysis.detailedComparison = compareTexts(originalText, whisperResult.transcript);
    
    // Analyze pronunciation issues
    analysis.pronunciationIssues = detectPronunciationIssues(
        originalText, 
        whisperResult.transcript, 
        whisperResult.segments
    );
    
    // Analyze pacing issues
    analysis.pacingIssues = detectPacingIssues(whisperResult.segments);
    
    // Calculate overall score
    analysis.overallScore = calculateOverallScore(analysis);
    
    // Generate recommendations
    analysis.recommendations = generateRecommendations(analysis);
    
    return analysis;
}

/**
 * Compare original text with transcribed text word by word
 */
function compareTexts(originalText, transcribedText) {
    const originalWords = normalizeText(originalText).split(/\s+/).filter(w => w.length > 0);
    const transcribedWords = normalizeText(transcribedText).split(/\s+/).filter(w => w.length > 0);
    
    const comparison = {
        originalWords,
        transcribedWords,
        matches: [],
        mismatches: [],
        accuracy: 0
    };
    
    // Simple word-by-word comparison (could be enhanced with fuzzy matching)
    const maxLength = Math.max(originalWords.length, transcribedWords.length);
    let correctWords = 0;
    
    for (let i = 0; i < maxLength; i++) {
        const original = originalWords[i] || '';
        const transcribed = transcribedWords[i] || '';
        
        if (original.toLowerCase() === transcribed.toLowerCase()) {
            comparison.matches.push({
                position: i,
                word: original,
                status: 'correct'
            });
            correctWords++;
        } else {
            comparison.mismatches.push({
                position: i,
                expected: original,
                actual: transcribed,
                similarity: calculateSimilarity(original, transcribed)
            });
        }
    }
    
    comparison.accuracy = originalWords.length > 0 ? (correctWords / originalWords.length) * 100 : 0;
    
    return comparison;
}

/**
 * Detect pronunciation issues based on text comparison and confidence scores
 */
function detectPronunciationIssues(originalText, transcribedText, segments) {
    const issues = [];
    
    if (!segments || segments.length === 0) {
        return issues;
    }
    
    // Analyze each segment for pronunciation issues
    segments.forEach((segment, segmentIndex) => {
        if (segment.words && segment.words.length > 0) {
            segment.words.forEach((wordData, wordIndex) => {
                const word = wordData.word;
                const probability = wordData.probability || 0;
                
                // Flag low confidence words as potential pronunciation issues
                if (probability < 0.7) {
                    issues.push({
                        type: 'low_confidence',
                        word: word,
                        position: { segment: segmentIndex, word: wordIndex },
                        confidence: probability,
                        timestamp: { start: wordData.start, end: wordData.end },
                        severity: probability < 0.5 ? 'high' : 'medium',
                        recommendation: `The word "${word}" was pronounced with low confidence (${(probability * 100).toFixed(1)}%). Consider practicing this word's pronunciation.`
                    });
                }
                
                // Check for common mispronunciation patterns
                const mispronunciationIssue = checkCommonMispronunciations(word, originalText);
                if (mispronunciationIssue) {
                    issues.push({
                        type: 'mispronunciation',
                        word: word,
                        position: { segment: segmentIndex, word: wordIndex },
                        timestamp: { start: wordData.start, end: wordData.end },
                        severity: 'high',
                        recommendation: mispronunciationIssue.recommendation
                    });
                }
            });
        }
    });
    
    return issues;
}

/**
 * Detect pacing issues based on word timing
 */
function detectPacingIssues(segments) {
    const issues = [];
    
    if (!segments || segments.length === 0) {
        return issues;
    }
    
    // Analyze speaking rate and pauses
    segments.forEach((segment, segmentIndex) => {
        if (segment.words && segment.words.length > 1) {
            const segmentDuration = segment.end - segment.start;
            const wordCount = segment.words.length;
            const wordsPerSecond = wordCount / segmentDuration;
            
            // Check for speaking too fast (>3 words per second)
            if (wordsPerSecond > 3) {
                issues.push({
                    type: 'speaking_too_fast',
                    segment: segmentIndex,
                    timestamp: { start: segment.start, end: segment.end },
                    rate: wordsPerSecond,
                    severity: wordsPerSecond > 4 ? 'high' : 'medium',
                    recommendation: `Speaking too fast in this segment (${wordsPerSecond.toFixed(1)} words/sec). Try to slow down for better clarity.`
                });
            }
            
            // Check for speaking too slow (<1 word per second)
            if (wordsPerSecond < 1 && segmentDuration > 3) {
                issues.push({
                    type: 'speaking_too_slow',
                    segment: segmentIndex,
                    timestamp: { start: segment.start, end: segment.end },
                    rate: wordsPerSecond,
                    severity: 'medium',
                    recommendation: `Speaking too slowly in this segment (${wordsPerSecond.toFixed(1)} words/sec). Try to maintain a more natural pace.`
                });
            }
            
            // Check for unusual pauses between words
            for (let i = 1; i < segment.words.length; i++) {
                const prevWord = segment.words[i - 1];
                const currentWord = segment.words[i];
                const pause = currentWord.start - prevWord.end;
                
                if (pause > 1.0) { // Pause longer than 1 second
                    issues.push({
                        type: 'long_pause',
                        position: { segment: segmentIndex, between: [i - 1, i] },
                        timestamp: { start: prevWord.end, end: currentWord.start },
                        duration: pause,
                        severity: pause > 2 ? 'high' : 'medium',
                        recommendation: `Unusually long pause (${pause.toFixed(1)}s) between "${prevWord.word}" and "${currentWord.word}". Try to maintain smoother flow.`
                    });
                }
            }
        }
    });
    
    return issues;
}

/**
 * Check for common mispronunciation patterns
 */
function checkCommonMispronunciations(word, originalText) {
    const commonIssues = [
        {
            pattern: /th/i,
            mispronunciation: /[td]/i,
            recommendation: "Practice the 'th' sound by placing your tongue between your teeth."
        },
        {
            pattern: /ing$/i,
            mispronunciation: /in$/i,
            recommendation: "Make sure to pronounce the 'g' sound at the end of '-ing' words."
        },
        {
            pattern: /ed$/i,
            mispronunciation: /[^td]$/i,
            recommendation: "Practice the correct pronunciation of past tense '-ed' endings."
        }
    ];
    
    // This is a simplified check - in a real implementation, you'd want more sophisticated analysis
    for (const issue of commonIssues) {
        if (issue.pattern.test(word) && originalText.toLowerCase().includes(word.toLowerCase())) {
            return {
                type: 'common_mispronunciation',
                recommendation: issue.recommendation
            };
        }
    }
    
    return null;
}

/**
 * Calculate overall pronunciation and pacing score
 */
function calculateOverallScore(analysis) {
    let score = 100;
    
    // Deduct points for pronunciation issues
    analysis.pronunciationIssues.forEach(issue => {
        if (issue.severity === 'high') score -= 10;
        else if (issue.severity === 'medium') score -= 5;
        else score -= 2;
    });
    
    // Deduct points for pacing issues
    analysis.pacingIssues.forEach(issue => {
        if (issue.severity === 'high') score -= 8;
        else if (issue.severity === 'medium') score -= 4;
        else score -= 2;
    });
    
    return Math.max(0, score);
}

/**
 * Generate recommendations based on analysis
 */
function generateRecommendations(analysis) {
    const recommendations = [];
    
    // Add specific recommendations from issues
    analysis.pronunciationIssues.forEach(issue => {
        if (issue.recommendation) {
            recommendations.push({
                type: 'pronunciation',
                priority: issue.severity,
                text: issue.recommendation,
                timestamp: issue.timestamp
            });
        }
    });
    
    analysis.pacingIssues.forEach(issue => {
        if (issue.recommendation) {
            recommendations.push({
                type: 'pacing',
                priority: issue.severity,
                text: issue.recommendation,
                timestamp: issue.timestamp
            });
        }
    });
    
    // Add general recommendations based on overall score
    if (analysis.overallScore < 70) {
        recommendations.push({
            type: 'general',
            priority: 'high',
            text: 'Consider practicing the identified words and phrases before recording again.',
            timestamp: null
        });
    }
    
    if (analysis.pronunciationIssues.length > 3) {
        recommendations.push({
            type: 'general',
            priority: 'medium',
            text: 'Multiple pronunciation issues detected. Consider slowing down and focusing on clear articulation.',
            timestamp: null
        });
    }
    
    if (analysis.pacingIssues.length > 2) {
        recommendations.push({
            type: 'general',
            priority: 'medium',
            text: 'Pacing inconsistencies detected. Try to maintain a steady, natural speaking rhythm.',
            timestamp: null
        });
    }
    
    return recommendations;
}

/**
 * Normalize text for comparison
 */
function normalizeText(text) {
    return text
        .toLowerCase()
        .replace(/[^\w\s]/g, '') // Remove punctuation
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();
}

/**
 * Calculate similarity between two words (simple Levenshtein-based)
 */
function calculateSimilarity(word1, word2) {
    if (!word1 || !word2) return 0;
    
    const len1 = word1.length;
    const len2 = word2.length;
    const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
    
    for (let i = 0; i <= len1; i++) matrix[i][0] = i;
    for (let j = 0; j <= len2; j++) matrix[0][j] = j;
    
    for (let i = 1; i <= len1; i++) {
        for (let j = 1; j <= len2; j++) {
            const cost = word1[i - 1] === word2[j - 1] ? 0 : 1;
            matrix[i][j] = Math.min(
                matrix[i - 1][j] + 1,
                matrix[i][j - 1] + 1,
                matrix[i - 1][j - 1] + cost
            );
        }
    }
    
    const maxLen = Math.max(len1, len2);
    return maxLen === 0 ? 1 : (maxLen - matrix[len1][len2]) / maxLen;
}
