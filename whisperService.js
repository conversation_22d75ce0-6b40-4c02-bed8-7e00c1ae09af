/********************************************************************
 *  whisperService.js
 *  ---------------------------------------------------------------
 *  Local Whisper integration for audio transcription
 *******************************************************************/

import * as dom from './domElements.js';
import * as state from './state.js';
import * as ui from './ui.js';
import { WHISPER_MODELS, DEFAULT_WHISPER_MODEL, WHISPER_ENDPOINT } from './constants.js';
import { analyzeTranscription } from './pronunciationAnalyzer.js';
import { getCorrespondingTextContent } from './aiVerification.js';

// Hold references to UI functions passed during init
let uiUpdateStatus = () => {};
let uiShowSSMLModal = () => {};
let uiHideSSMLModal = () => {};

/**
 * Initialize Whisper service with UI callbacks
 */
export function initWhisperService(uiHelpers) {
    uiUpdateStatus = uiHelpers.updateStatus;
    uiShowSSMLModal = uiHelpers.showSSMLModal;
    uiHideSSMLModal = uiHelpers.hideSSMLModal;

    // Populate model selector
    populateModelSelector();

    // Set up event listeners
    if (dom.whisperAudioCheckBtn) {
        dom.whisperAudioCheckBtn.addEventListener('click', showWhisperModal);
    }
    if (dom.whisperModalCloseBtn) {
        dom.whisperModalCloseBtn.addEventListener('click', hideWhisperModal);
    }
    if (dom.whisperStartCheckBtn) {
        dom.whisperStartCheckBtn.addEventListener('click', handleStartWhisperCheck);
    }
    if (dom.whisperSingleFileRadio) {
        dom.whisperSingleFileRadio.addEventListener('change', updateFileSelectionUI);
    }
    if (dom.whisperAllFilesRadio) {
        dom.whisperAllFilesRadio.addEventListener('change', updateFileSelectionUI);
    }

    // Check server status on initialization
    checkWhisperServerStatus();
}

/**
 * Show the Whisper Audio Check modal
 */
export function showWhisperModal() {
    if (!dom.whisperAudioCheckModal) return;
    
    // Populate file selector with available audio files
    populateFileSelector();
    
    // Update UI based on current selection
    updateFileSelectionUI();
    
    // Show modal
    uiShowSSMLModal(dom.whisperAudioCheckModal);
}

/**
 * Hide the Whisper Audio Check modal
 */
export function hideWhisperModal() {
    if (!dom.whisperAudioCheckModal) return;
    uiHideSSMLModal(dom.whisperAudioCheckModal);
}

/**
 * Populate the model selector with available Whisper models
 */
function populateModelSelector() {
    if (!dom.whisperModelSelect) return;

    // Clear existing options
    dom.whisperModelSelect.innerHTML = '';

    // Add model options
    WHISPER_MODELS.forEach(model => {
        const option = document.createElement('option');
        option.value = model.value;
        option.textContent = model.name;
        if (model.value === DEFAULT_WHISPER_MODEL) {
            option.selected = true;
        }
        dom.whisperModelSelect.appendChild(option);
    });
}

/**
 * Check if the local Whisper server is running
 */
async function checkWhisperServerStatus() {
    try {
        const response = await fetch(`${WHISPER_ENDPOINT.replace('/transcribe', '/health')}`, {
            method: 'GET',
            signal: AbortSignal.timeout(3000) // 3 second timeout
        });

        if (response.ok) {
            const data = await response.json();
            console.log('Whisper server is running:', data);
            return true;
        } else {
            console.warn('Whisper server responded with error:', response.status);
            return false;
        }
    } catch (error) {
        console.warn('Whisper server is not running:', error.message);
        return false;
    }
}

/**
 * Populate the file selector dropdown with available audio files
 */
function populateFileSelector() {
    if (!dom.whisperFileSelect) return;
    
    // Clear existing options
    dom.whisperFileSelect.innerHTML = '';
    
    if (state.musicFiles.length === 0) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = 'No audio files loaded';
        option.disabled = true;
        dom.whisperFileSelect.appendChild(option);
        return;
    }
    
    // Add audio files as options
    state.musicFiles.forEach(file => {
        const option = document.createElement('option');
        option.value = file.id;
        option.textContent = file.name;
        dom.whisperFileSelect.appendChild(option);
    });
}

/**
 * Update the file selection UI based on radio button selection
 */
function updateFileSelectionUI() {
    if (!dom.whisperSingleFileControls || !dom.whisperSingleFileRadio) return;
    
    const isSingleMode = dom.whisperSingleFileRadio.checked;
    dom.whisperSingleFileControls.style.display = isSingleMode ? 'block' : 'none';
}

/**
 * Handle the start of Whisper transcription check
 */
async function handleStartWhisperCheck() {
    // Check if Whisper server is running
    const serverRunning = await checkWhisperServerStatus();
    if (!serverRunning) {
        updateWhisperOutput('Error: Local Whisper server is not running. Please start the server with: python whisper_server.py', 'error');
        return;
    }

    const isSingleMode = dom.whisperSingleFileRadio?.checked;
    const selectedModel = dom.whisperModelSelect?.value || DEFAULT_WHISPER_MODEL;

    if (isSingleMode) {
        const selectedFileId = dom.whisperFileSelect?.value;
        if (!selectedFileId) {
            updateWhisperOutput('Error: Please select an audio file.', 'error');
            return;
        }

        const audioFile = state.musicFiles.find(f => f.id === selectedFileId);
        if (!audioFile) {
            updateWhisperOutput('Error: Selected audio file not found.', 'error');
            return;
        }

        await transcribeSingleFile(audioFile, selectedModel);
    } else {
        // Check all files
        await transcribeAllFiles(selectedModel);
    }
}

/**
 * Transcribe a single audio file
 */
async function transcribeSingleFile(audioFile, model) {
    updateWhisperOutput(`Starting transcription of "${audioFile.name}" using ${model}...`, 'info');

    try {
        const whisperResult = await transcribeAudioFileDetailed(audioFile, model);

        // Try to get corresponding text for comparison
        let originalText = null;
        try {
            originalText = await getCorrespondingTextContent(audioFile);
        } catch (error) {
            console.log('No corresponding text found for analysis:', error.message);
        }

        // Perform pronunciation and pacing analysis
        let analysis = null;
        if (originalText) {
            analysis = analyzeTranscription(originalText, whisperResult);
        }

        const output = generateDetailedTranscriptionOutput(audioFile.name, model, whisperResult, analysis);
        updateWhisperOutput(output, 'success');
    } catch (error) {
        updateWhisperOutput(`Error transcribing "${audioFile.name}": ${error.message}`, 'error');
    }
}

/**
 * Transcribe all audio files
 */
async function transcribeAllFiles(model) {
    if (state.musicFiles.length === 0) {
        updateWhisperOutput('No audio files to transcribe.', 'warning');
        return;
    }

    updateWhisperOutput(`Starting transcription of ${state.musicFiles.length} files using ${model}...`, 'info');

    let results = '';
    let successCount = 0;
    let errorCount = 0;
    let totalIssues = 0;

    for (let i = 0; i < state.musicFiles.length; i++) {
        const audioFile = state.musicFiles[i];

        try {
            updateWhisperOutput(`Processing ${i + 1}/${state.musicFiles.length}: "${audioFile.name}"...`, 'info');

            const whisperResult = await transcribeAudioFileDetailed(audioFile, model);

            // Try to get corresponding text for analysis
            let originalText = null;
            let analysis = null;
            try {
                originalText = await getCorrespondingTextContent(audioFile);
                if (originalText) {
                    analysis = analyzeTranscription(originalText, whisperResult);
                    totalIssues += analysis.pronunciationIssues.length + analysis.pacingIssues.length;
                }
            } catch (error) {
                console.log(`No corresponding text found for ${audioFile.name}:`, error.message);
            }

            results += generateDetailedTranscriptionOutput(
                `File ${i + 1}: ${audioFile.name}`,
                model,
                whisperResult,
                analysis
            );

            successCount++;
        } catch (error) {
            results += `
<div class="whisper-result error">
    <h5>File ${i + 1}: ${audioFile.name}</h5>
    <div class="transcript-content">
        <p class="error">Error: ${error.message}</p>
    </div>
</div>`;

            errorCount++;
        }
    }

    const summary = `
<div class="whisper-summary">
    <h4>Transcription Complete</h4>
    <p>Model: ${model}</p>
    <p>Total files: ${state.musicFiles.length}</p>
    <p>Successful: ${successCount}</p>
    <p>Errors: ${errorCount}</p>
    <p>Total pronunciation/pacing issues detected: ${totalIssues}</p>
</div>`;

    updateWhisperOutput(summary + results, 'success');
}

/**
 * Transcribe an audio file using local Whisper server (simple version for backward compatibility)
 */
async function transcribeAudioFile(audioFile, model = DEFAULT_WHISPER_MODEL) {
    const result = await transcribeAudioFileDetailed(audioFile, model);
    return result.transcript;
}

/**
 * Transcribe an audio file using local Whisper server with detailed results
 */
async function transcribeAudioFileDetailed(audioFile, model = DEFAULT_WHISPER_MODEL) {
    // Create FormData for the local server request
    const formData = new FormData();
    formData.append('file', audioFile.file);
    formData.append('model', model);

    const response = await fetch(WHISPER_ENDPOINT, {
        method: 'POST',
        body: formData
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
        throw new Error(result.error || 'Transcription failed');
    }

    return {
        transcript: result.transcript.trim(),
        segments: result.segments || [],
        language: result.language || 'unknown',
        model: result.model || model,
        filename: result.filename || audioFile.name
    };
}

/**
 * Generate detailed transcription output with pronunciation and pacing analysis
 */
function generateDetailedTranscriptionOutput(filename, model, whisperResult, analysis) {
    let output = `
<div class="whisper-result">
    <h5>File: ${filename}</h5>
    <h6>Model: ${model}</h6>
    <div class="transcript-content">
        <strong>Transcription:</strong>
        <p>${whisperResult.transcript}</p>
    </div>`;

    if (analysis) {
        output += `
    <div class="analysis-section">
        <h6>Pronunciation & Pacing Analysis</h6>
        <div class="analysis-score">
            <strong>Overall Score: ${analysis.overallScore}/100</strong>
            ${getScoreColor(analysis.overallScore)}
        </div>`;

        // Show pronunciation issues
        if (analysis.pronunciationIssues.length > 0) {
            output += `
        <div class="pronunciation-issues">
            <h7>Pronunciation Issues (${analysis.pronunciationIssues.length}):</h7>
            <ul>`;
            analysis.pronunciationIssues.forEach(issue => {
                const timeStr = issue.timestamp ?
                    ` at ${formatTime(issue.timestamp.start)}-${formatTime(issue.timestamp.end)}` : '';
                output += `
                <li class="issue-${issue.severity}">
                    <strong>"${issue.word}"</strong>${timeStr}: ${issue.recommendation}
                </li>`;
            });
            output += `</ul>
        </div>`;
        }

        // Show pacing issues
        if (analysis.pacingIssues.length > 0) {
            output += `
        <div class="pacing-issues">
            <h7>Pacing Issues (${analysis.pacingIssues.length}):</h7>
            <ul>`;
            analysis.pacingIssues.forEach(issue => {
                const timeStr = issue.timestamp ?
                    ` at ${formatTime(issue.timestamp.start)}-${formatTime(issue.timestamp.end)}` : '';
                output += `
                <li class="issue-${issue.severity}">
                    ${issue.recommendation}${timeStr}
                </li>`;
            });
            output += `</ul>
        </div>`;
        }

        // Show general recommendations
        if (analysis.recommendations.length > 0) {
            const generalRecs = analysis.recommendations.filter(r => r.type === 'general');
            if (generalRecs.length > 0) {
                output += `
        <div class="general-recommendations">
            <h7>General Recommendations:</h7>
            <ul>`;
                generalRecs.forEach(rec => {
                    output += `<li class="rec-${rec.priority}">${rec.text}</li>`;
                });
                output += `</ul>
        </div>`;
            }
        }

        output += `
    </div>`;
    } else {
        output += `
    <div class="analysis-section">
        <p class="no-analysis">No corresponding text found for pronunciation analysis.</p>
    </div>`;
    }

    output += `
</div>`;

    return output;
}

/**
 * Get score color indicator
 */
function getScoreColor(score) {
    if (score >= 90) return '<span class="score-excellent">Excellent</span>';
    if (score >= 80) return '<span class="score-good">Good</span>';
    if (score >= 70) return '<span class="score-fair">Fair</span>';
    return '<span class="score-poor">Needs Improvement</span>';
}

/**
 * Format time in seconds to MM:SS format
 */
function formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
}

/**
 * Update the Whisper output area with new content
 */
function updateWhisperOutput(content, type = 'info') {
    if (!dom.whisperOutputArea) return;

    const timestamp = new Date().toLocaleTimeString();
    const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';

    if (type === 'info' && content.includes('Starting transcription')) {
        // Clear previous results when starting new transcription
        dom.whisperOutputArea.innerHTML = `<div class="whisper-log ${className}"><small>${timestamp}</small> ${content}</div>`;
    } else {
        // Append to existing content
        const logEntry = document.createElement('div');
        logEntry.className = `whisper-log ${className}`;
        logEntry.innerHTML = content.includes('<div') ? content : `<small>${timestamp}</small> ${content}`;
        dom.whisperOutputArea.appendChild(logEntry);
    }

    // Scroll to bottom
    dom.whisperOutputArea.scrollTop = dom.whisperOutputArea.scrollHeight;
}
