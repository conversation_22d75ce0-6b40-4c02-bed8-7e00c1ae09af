<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pronunciation Analysis Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background-color: #e8f5e8;
            border-left-color: #4caf50;
            color: #2e7d32;
        }
        .error {
            background-color: #ffebee;
            border-left-color: #f44336;
            color: #c62828;
        }
        .info {
            background-color: #e3f2fd;
            border-left-color: #2196f3;
            color: #0d47a1;
        }
        button {
            background-color: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1976d2;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Pronunciation Analysis Test</h1>
    
    <div class="test-container">
        <h2>Test Pronunciation Analyzer</h2>
        <p>This test verifies the pronunciation analysis functionality without requiring audio files.</p>
        
        <button onclick="testBasicAnalysis()">Test Basic Analysis</button>
        <button onclick="testPronunciationIssues()">Test Pronunciation Issues</button>
        <button onclick="testPacingIssues()">Test Pacing Issues</button>
        <button onclick="testCompleteAnalysis()">Test Complete Analysis</button>
        
        <div id="testResults"></div>
    </div>

    <script type="module">
        import { analyzeTranscription } from './pronunciationAnalyzer.js';

        window.analyzeTranscription = analyzeTranscription;

        window.testBasicAnalysis = function() {
            const originalText = "Hello world, this is a test.";
            const whisperResult = {
                transcript: "Hello world, this is a test.",
                segments: [{
                    start: 0,
                    end: 3,
                    text: "Hello world, this is a test.",
                    words: [
                        { word: "Hello", start: 0, end: 0.5, probability: 0.95 },
                        { word: "world", start: 0.6, end: 1.0, probability: 0.92 },
                        { word: "this", start: 1.5, end: 1.8, probability: 0.88 },
                        { word: "is", start: 1.9, end: 2.0, probability: 0.90 },
                        { word: "a", start: 2.1, end: 2.2, probability: 0.85 },
                        { word: "test", start: 2.3, end: 2.8, probability: 0.93 }
                    ]
                }]
            };

            const analysis = analyzeTranscription(originalText, whisperResult);
            displayResult("Basic Analysis", analysis, "success");
        };

        window.testPronunciationIssues = function() {
            const originalText = "The quick brown fox jumps over the lazy dog.";
            const whisperResult = {
                transcript: "The quick brown fox jumps over the lazy dog.",
                segments: [{
                    start: 0,
                    end: 5,
                    text: "The quick brown fox jumps over the lazy dog.",
                    words: [
                        { word: "The", start: 0, end: 0.3, probability: 0.45 }, // Low confidence
                        { word: "quick", start: 0.4, end: 0.8, probability: 0.92 },
                        { word: "brown", start: 0.9, end: 1.3, probability: 0.88 },
                        { word: "fox", start: 1.4, end: 1.7, probability: 0.65 }, // Low confidence
                        { word: "jumps", start: 1.8, end: 2.2, probability: 0.90 },
                        { word: "over", start: 2.3, end: 2.6, probability: 0.85 },
                        { word: "the", start: 2.7, end: 2.9, probability: 0.55 }, // Low confidence
                        { word: "lazy", start: 3.0, end: 3.4, probability: 0.88 },
                        { word: "dog", start: 3.5, end: 3.8, probability: 0.92 }
                    ]
                }]
            };

            const analysis = analyzeTranscription(originalText, whisperResult);
            displayResult("Pronunciation Issues Test", analysis, "info");
        };

        window.testPacingIssues = function() {
            const originalText = "This is a test of pacing analysis.";
            const whisperResult = {
                transcript: "This is a test of pacing analysis.",
                segments: [{
                    start: 0,
                    end: 2, // Very fast: 7 words in 2 seconds = 3.5 words/sec
                    text: "This is a test of pacing analysis.",
                    words: [
                        { word: "This", start: 0, end: 0.1, probability: 0.95 },
                        { word: "is", start: 0.2, end: 0.25, probability: 0.92 },
                        { word: "a", start: 0.3, end: 0.35, probability: 0.88 },
                        { word: "test", start: 0.4, end: 0.6, probability: 0.90 },
                        { word: "of", start: 2.5, end: 2.6, probability: 0.85 }, // Long pause before this word
                        { word: "pacing", start: 2.7, end: 1.0, probability: 0.93 },
                        { word: "analysis", start: 1.1, end: 1.5, probability: 0.91 }
                    ]
                }]
            };

            const analysis = analyzeTranscription(originalText, whisperResult);
            displayResult("Pacing Issues Test", analysis, "info");
        };

        window.testCompleteAnalysis = function() {
            const originalText = "The weather is beautiful today, and I think we should go outside.";
            const whisperResult = {
                transcript: "The weather is beautiful today, and I think we should go outside.",
                segments: [{
                    start: 0,
                    end: 8,
                    text: "The weather is beautiful today, and I think we should go outside.",
                    words: [
                        { word: "The", start: 0, end: 0.3, probability: 0.45 }, // Low confidence
                        { word: "weather", start: 0.4, end: 0.9, probability: 0.92 },
                        { word: "is", start: 1.0, end: 1.1, probability: 0.88 },
                        { word: "beautiful", start: 1.2, end: 1.8, probability: 0.90 },
                        { word: "today", start: 1.9, end: 2.3, probability: 0.85 },
                        { word: "and", start: 3.8, end: 3.9, probability: 0.93 }, // Long pause before
                        { word: "I", start: 4.0, end: 4.1, probability: 0.91 },
                        { word: "think", start: 4.2, end: 4.5, probability: 0.60 }, // Low confidence
                        { word: "we", start: 4.6, end: 4.7, probability: 0.88 },
                        { word: "should", start: 4.8, end: 5.1, probability: 0.92 },
                        { word: "go", start: 5.2, end: 5.3, probability: 0.90 },
                        { word: "outside", start: 5.4, end: 5.9, probability: 0.85 }
                    ]
                }]
            };

            const analysis = analyzeTranscription(originalText, whisperResult);
            displayResult("Complete Analysis Test", analysis, "success");
        };

        function displayResult(testName, result, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            
            let html = `<h3>${testName}</h3>`;
            html += `<p><strong>Overall Score:</strong> ${result.overallScore}/100</p>`;
            html += `<p><strong>Pronunciation Issues:</strong> ${result.pronunciationIssues.length}</p>`;
            html += `<p><strong>Pacing Issues:</strong> ${result.pacingIssues.length}</p>`;
            html += `<p><strong>Recommendations:</strong> ${result.recommendations.length}</p>`;
            
            if (result.pronunciationIssues.length > 0) {
                html += `<h4>Pronunciation Issues:</h4><ul>`;
                result.pronunciationIssues.forEach(issue => {
                    html += `<li><strong>${issue.word}</strong> (${issue.severity}): ${issue.recommendation}</li>`;
                });
                html += `</ul>`;
            }
            
            if (result.pacingIssues.length > 0) {
                html += `<h4>Pacing Issues:</h4><ul>`;
                result.pacingIssues.forEach(issue => {
                    html += `<li>(${issue.severity}): ${issue.recommendation}</li>`;
                });
                html += `</ul>`;
            }
            
            html += `<details><summary>Full Analysis Object</summary><pre>${JSON.stringify(result, null, 2)}</pre></details>`;
            
            resultDiv.innerHTML = html;
            resultsDiv.appendChild(resultDiv);
        }
    </script>
</body>
</html>
