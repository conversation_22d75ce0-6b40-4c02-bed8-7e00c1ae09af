/********************************************************************
 *  whisperService.js
 *  ---------------------------------------------------------------
 *  Local Whisper integration for audio transcription
 *******************************************************************/

import * as dom from './domElements.js';
import * as state from './state.js';
import * as ui from './ui.js';
import { WHISPER_MODELS, DEFAULT_WHISPER_MODEL, WHISPER_ENDPOINT } from './constants.js';

// Hold references to UI functions passed during init
let uiUpdateStatus = () => {};
let uiShowSSMLModal = () => {};
let uiHideSSMLModal = () => {};

/**
 * Initialize Whisper service with UI callbacks
 */
export function initWhisperService(uiHelpers) {
    uiUpdateStatus = uiHelpers.updateStatus;
    uiShowSSMLModal = uiHelpers.showSSMLModal;
    uiHideSSMLModal = uiHelpers.hideSSMLModal;

    // Populate model selector
    populateModelSelector();

    // Set up event listeners
    if (dom.whisperAudioCheckBtn) {
        dom.whisperAudioCheckBtn.addEventListener('click', showWhisperModal);
    }
    if (dom.whisperModalCloseBtn) {
        dom.whisperModalCloseBtn.addEventListener('click', hideWhisperModal);
    }
    if (dom.whisperStartCheckBtn) {
        dom.whisperStartCheckBtn.addEventListener('click', handleStartWhisperCheck);
    }
    if (dom.whisperSingleFileRadio) {
        dom.whisperSingleFileRadio.addEventListener('change', updateFileSelectionUI);
    }
    if (dom.whisperAllFilesRadio) {
        dom.whisperAllFilesRadio.addEventListener('change', updateFileSelectionUI);
    }

    // Check server status on initialization
    checkWhisperServerStatus();
}

/**
 * Show the Whisper Audio Check modal
 */
export function showWhisperModal() {
    if (!dom.whisperAudioCheckModal) return;
    
    // Populate file selector with available audio files
    populateFileSelector();
    
    // Update UI based on current selection
    updateFileSelectionUI();
    
    // Show modal
    uiShowSSMLModal(dom.whisperAudioCheckModal);
}

/**
 * Hide the Whisper Audio Check modal
 */
export function hideWhisperModal() {
    if (!dom.whisperAudioCheckModal) return;
    uiHideSSMLModal(dom.whisperAudioCheckModal);
}

/**
 * Populate the model selector with available Whisper models
 */
function populateModelSelector() {
    if (!dom.whisperModelSelect) return;

    // Clear existing options
    dom.whisperModelSelect.innerHTML = '';

    // Add model options
    WHISPER_MODELS.forEach(model => {
        const option = document.createElement('option');
        option.value = model.value;
        option.textContent = model.name;
        if (model.value === DEFAULT_WHISPER_MODEL) {
            option.selected = true;
        }
        dom.whisperModelSelect.appendChild(option);
    });
}

/**
 * Check if the local Whisper server is running
 */
async function checkWhisperServerStatus() {
    try {
        const response = await fetch(`${WHISPER_ENDPOINT.replace('/transcribe', '/health')}`, {
            method: 'GET',
            signal: AbortSignal.timeout(3000) // 3 second timeout
        });

        if (response.ok) {
            const data = await response.json();
            console.log('Whisper server is running:', data);
            return true;
        } else {
            console.warn('Whisper server responded with error:', response.status);
            return false;
        }
    } catch (error) {
        console.warn('Whisper server is not running:', error.message);
        return false;
    }
}

/**
 * Populate the file selector dropdown with available audio files
 */
function populateFileSelector() {
    if (!dom.whisperFileSelect) return;
    
    // Clear existing options
    dom.whisperFileSelect.innerHTML = '';
    
    if (state.musicFiles.length === 0) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = 'No audio files loaded';
        option.disabled = true;
        dom.whisperFileSelect.appendChild(option);
        return;
    }
    
    // Add audio files as options
    state.musicFiles.forEach(file => {
        const option = document.createElement('option');
        option.value = file.id;
        option.textContent = file.name;
        dom.whisperFileSelect.appendChild(option);
    });
}

/**
 * Update the file selection UI based on radio button selection
 */
function updateFileSelectionUI() {
    if (!dom.whisperSingleFileControls || !dom.whisperSingleFileRadio) return;
    
    const isSingleMode = dom.whisperSingleFileRadio.checked;
    dom.whisperSingleFileControls.style.display = isSingleMode ? 'block' : 'none';
}

/**
 * Handle the start of Whisper transcription check
 */
async function handleStartWhisperCheck() {
    // Check if Whisper server is running
    const serverRunning = await checkWhisperServerStatus();
    if (!serverRunning) {
        updateWhisperOutput('Error: Local Whisper server is not running. Please start the server with: python whisper_server.py', 'error');
        return;
    }

    const isSingleMode = dom.whisperSingleFileRadio?.checked;
    const selectedModel = dom.whisperModelSelect?.value || DEFAULT_WHISPER_MODEL;

    if (isSingleMode) {
        const selectedFileId = dom.whisperFileSelect?.value;
        if (!selectedFileId) {
            updateWhisperOutput('Error: Please select an audio file.', 'error');
            return;
        }

        const audioFile = state.musicFiles.find(f => f.id === selectedFileId);
        if (!audioFile) {
            updateWhisperOutput('Error: Selected audio file not found.', 'error');
            return;
        }

        await transcribeSingleFile(audioFile, selectedModel);
    } else {
        // Check all files
        await transcribeAllFiles(selectedModel);
    }
}

/**
 * Transcribe a single audio file
 */
async function transcribeSingleFile(audioFile, model) {
    updateWhisperOutput(`Starting transcription of "${audioFile.name}" using ${model}...`, 'info');
    
    try {
        const transcript = await transcribeAudioFile(audioFile, model);
        
        const output = `
<div class="whisper-result">
    <h5>File: ${audioFile.name}</h5>
    <h6>Model: ${model}</h6>
    <div class="transcript-content">
        <strong>Transcription:</strong>
        <p>${transcript}</p>
    </div>
</div>`;
        
        updateWhisperOutput(output, 'success');
    } catch (error) {
        updateWhisperOutput(`Error transcribing "${audioFile.name}": ${error.message}`, 'error');
    }
}

/**
 * Transcribe all audio files
 */
async function transcribeAllFiles(model) {
    if (state.musicFiles.length === 0) {
        updateWhisperOutput('No audio files to transcribe.', 'warning');
        return;
    }
    
    updateWhisperOutput(`Starting transcription of ${state.musicFiles.length} files using ${model}...`, 'info');
    
    let results = '';
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < state.musicFiles.length; i++) {
        const audioFile = state.musicFiles[i];
        
        try {
            updateWhisperOutput(`Processing ${i + 1}/${state.musicFiles.length}: "${audioFile.name}"...`, 'info');
            
            const transcript = await transcribeAudioFile(audioFile, model);
            
            results += `
<div class="whisper-result">
    <h5>File ${i + 1}: ${audioFile.name}</h5>
    <div class="transcript-content">
        <p>${transcript}</p>
    </div>
</div>`;
            
            successCount++;
        } catch (error) {
            results += `
<div class="whisper-result error">
    <h5>File ${i + 1}: ${audioFile.name}</h5>
    <div class="transcript-content">
        <p class="error">Error: ${error.message}</p>
    </div>
</div>`;
            
            errorCount++;
        }
    }
    
    const summary = `
<div class="whisper-summary">
    <h4>Transcription Complete</h4>
    <p>Model: ${model}</p>
    <p>Total files: ${state.musicFiles.length}</p>
    <p>Successful: ${successCount}</p>
    <p>Errors: ${errorCount}</p>
</div>`;
    
    updateWhisperOutput(summary + results, 'success');
}

/**
 * Transcribe an audio file using local Whisper server
 */
async function transcribeAudioFile(audioFile, model = DEFAULT_WHISPER_MODEL) {
    // Create FormData for the local server request
    const formData = new FormData();
    formData.append('file', audioFile.file);
    formData.append('model', model);

    const response = await fetch(WHISPER_ENDPOINT, {
        method: 'POST',
        body: formData
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
        throw new Error(result.error || 'Transcription failed');
    }

    return result.transcript.trim();
}

/**
 * Update the Whisper output area with new content
 */
function updateWhisperOutput(content, type = 'info') {
    if (!dom.whisperOutputArea) return;
    
    const timestamp = new Date().toLocaleTimeString();
    const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
    
    if (type === 'info' && content.includes('Starting transcription')) {
        // Clear previous results when starting new transcription
        dom.whisperOutputArea.innerHTML = `<div class="whisper-log ${className}"><small>${timestamp}</small> ${content}</div>`;
    } else {
        // Append to existing content
        const logEntry = document.createElement('div');
        logEntry.className = `whisper-log ${className}`;
        logEntry.innerHTML = content.includes('<div') ? content : `<small>${timestamp}</small> ${content}`;
        dom.whisperOutputArea.appendChild(logEntry);
    }
    
    // Scroll to bottom
    dom.whisperOutputArea.scrollTop = dom.whisperOutputArea.scrollHeight;
}
