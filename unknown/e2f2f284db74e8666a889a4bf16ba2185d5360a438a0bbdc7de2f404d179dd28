import { DEFAULT_G_TTS_VOICE_NAME, G_TTS_API_KEY, MS_TTS_API_KEY, DEFAULT_MS_TTS_VOICE_NAME, MS_TTS_SERVICE_REGION } from './constants.js';
import * as state from './state.js';
import * as dom from './domElements.js';
import * as ui from './ui.js';
import * as fileService from './fileService.js';
import * as audioService from './audioService.js';
import * as documentService from './documentService.js';
import * as ttsService from './ttsService.js';
import * as ssmlEditor from './ssmlEditor.js';
import * as aiVerification from './aiVerification.js';
import * as themeService from './themeService.js';
import * as aiVoiceCreator from './aiVoiceCreator.js';
import { initWhisperService } from '../whisperService.js';

// --- Initialize ---
document.addEventListener('DOMContentLoaded', async () => { // <-- Made async
    dom.initDOMelements();
    state.setSelectedGTTSVoiceName(DEFAULT_G_TTS_VOICE_NAME);
    state.setSelectedMsTTSVoiceName(DEFAULT_MS_TTS_VOICE_NAME);

    // Initialize Theme Service (now async)
    await themeService.initThemes(); // <-- Added await
    ui.populateThemeSelector(themeService.getAvailableThemes(), state.currentThemeName);
    themeService.applyTheme(state.currentThemeName);


    ui.registerUICallbacks({
        selectMusicTrack: audioService.selectMusicTrack,
        selectDocument: documentService.selectDocument,
        insertBreakTag: documentService.insertBreakTag,
    });

    ssmlEditor.initSsmEditorEventListeners({
        showSSMLModal: ui.showSSMLModal,
        hideSSMLModal: ui.hideSSMLModal,
        updateSSMLStatusBar: ui.updateSSMLStatusBar,
        ssmlSimpleHighlight: ssmlEditor.ssmlSimpleHighlight
    });

    aiVerification.initAiVerificationEventListeners({
        updateStatus: ui.updateStatus,
        showSSMLModal: ui.showSSMLModal,
        hideSSMLModal: ui.hideSSMLModal,
    });

    aiVoiceCreator.initAiVoiceCreator({
        updateStatus: ui.updateStatus,
        populateGoogleVoiceSelector: ui.populateGoogleVoiceSelector,
        populateMicrosoftVoiceSelector: ui.populateMicrosoftVoiceSelector
    });

    initWhisperService({
        updateStatus: ui.updateStatus,
        showSSMLModal: ui.showSSMLModal,
        hideSSMLModal: ui.hideSSMLModal
    });


    ui.updateAudioControlsUI();
    ui.renderBreakTagButtons();
    ui.setActiveView('verification');

    if (!G_TTS_API_KEY) {
        ui.updateStatus('Warning: Google TTS API Key not available. Google reprocessing disabled.');
        console.warn("G_TTS_API_KEY is not available. Google TTS features will be disabled.");
        ui.populateGoogleVoiceSelector(dom.gTtsVoiceSelect, [], '');
        ui.populateGoogleVoiceSelector(dom.aiVoiceTTSVoiceSelect, [], '');
        dom.gTtsVoiceSelect.disabled = true;
        dom.reprocessGTTSBtn.disabled = true;
    } else {
        ttsService.fetchAvailableGoogleVoices();
    }

    if (!MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION) {
        let msWarning = 'Warning: Microsoft TTS API Key or Service Region not available. Microsoft reprocessing disabled.';
        if (!MS_TTS_API_KEY) console.warn("MS_TTS_API_KEY is not available.");
        if (!MS_TTS_SERVICE_REGION) console.warn("MS_TTS_SERVICE_REGION is not available.");
        ui.updateStatus(msWarning);
        ui.populateMicrosoftVoiceSelector(dom.msTtsVoiceSelect, [], '');
        dom.msTtsVoiceSelect.disabled = true;
        dom.reprocessMsTTSBtn.disabled = true;
    } else {
        ttsService.fetchAvailableMicrosoftVoices();
    }

    // --- Event Listeners (Main App) ---
    if (dom.audiobookVerificationBtn) {
        dom.audiobookVerificationBtn.addEventListener('click', () => ui.setActiveView('verification'));
    }
    if (dom.audiobookTextEditorBtn) {
        dom.audiobookTextEditorBtn.addEventListener('click', () => ui.setActiveView('textEditor'));
    }
    if (dom.aiVoiceCreatorBtn) {
        dom.aiVoiceCreatorBtn.addEventListener('click', () => ui.setActiveView('aiVoiceCreator'));
    }


    dom.musicFolderInput.addEventListener('change', (e) => fileService.handleFolderSelection(e, 'music'));
    dom.docFolderInput.addEventListener('change', (e) => fileService.handleFolderSelection(e, 'docs'));

    dom.playPauseBtn.addEventListener('click', audioService.togglePlayPause);
    dom.prevTrackBtn.addEventListener('click', audioService.playPrevTrack);
    dom.nextTrackBtn.addEventListener('click', audioService.playNextTrack);
    dom.progressBar.addEventListener('input', audioService.handleSeek);
    dom.volumeSlider.addEventListener('input', audioService.handleVolumeChange);

    dom.audioPlayer.addEventListener('loadedmetadata', audioService.handleAudioLoadedMetadata);
    dom.audioPlayer.addEventListener('timeupdate', audioService.handleAudioTimeUpdate);
    dom.audioPlayer.addEventListener('ended', audioService.handleAudioEnded);
    dom.audioPlayer.addEventListener('play', () => {
        state.setIsPlaying(true);
        state.setIsPausedManually(false);
        ui.updatePlayPauseButton();
        ui.updateStatus(`Playing: ${state.currentAudioFile?.name || 'track'}`);
    });
    dom.audioPlayer.addEventListener('pause', () => {
        state.setIsPlaying(false);
        ui.updatePlayPauseButton();
        if (state.isPausedManually) {
             ui.updateStatus('Paused.');
        }
    });

    dom.toggleEditBtn.addEventListener('click', documentService.toggleEditMode);
    dom.saveDocBtn.addEventListener('click', documentService.saveDocument);

    dom.reprocessGTTSBtn.addEventListener('click', ttsService.handleReprocessWithGoogleTTS);
    dom.gTtsVoiceSelect.addEventListener('change', (e) => {
        state.setSelectedGTTSVoiceName(e.target.value);
    });

    dom.reprocessMsTTSBtn.addEventListener('click', ttsService.handleReprocessWithMicrosoftTTS);
    dom.msTtsVoiceSelect.addEventListener('change', (e) => {
        state.setSelectedMsTTSVoiceName(e.target.value);
    });

    if (dom.themeSelect) {
        dom.themeSelect.addEventListener('change', (e) => {
            const themeName = e.target.value;
            themeService.applyTheme(themeName);
        });
    }

    console.log("Vanilla JS App Initialized (Modularized)");
});